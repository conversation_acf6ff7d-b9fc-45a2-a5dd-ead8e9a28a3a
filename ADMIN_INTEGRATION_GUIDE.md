# XWiki 管理界面集成指南

## 问题诊断

如果管理界面没有出现，请按以下步骤检查：

### 1. 检查JAR文件是否正确安装
- 确认 `xwiki-extension-watermark-1.0-SNAPSHOT.jar` 已复制到 `WEB-INF/lib/` 目录
- 重启Tomcat服务器

### 2. 检查页面是否已导入
访问以下URL检查页面是否存在：
- `http://localhost:8080/xwiki/bin/view/XWiki/WatermarkAdmin`
- `http://localhost:8080/xwiki/bin/view/XWiki/WatermarkAdminTranslations`

### 3. 手动导入页面（如果自动导入失败）
如果页面不存在，需要手动导入：

1. 访问 `http://localhost:8080/xwiki/bin/admin/XWiki/XWikiPreferences?editor=globaladmin&section=Import`
2. 上传JAR文件或解压JAR文件中的XML文件
3. 导入以下页面：
   - `XWiki.WatermarkAdmin`
   - `XWiki.WatermarkAdminTranslations`
   - `XWiki.WatermarkAdminTranslations_zh`
   - `XWiki.WatermarkSkinExtension`

### 4. 直接访问管理页面
即使管理界面没有出现在左侧菜单中，您也可以直接访问：
`http://localhost:8080/xwiki/bin/admin/XWiki/XWikiPreferences?editor=globaladmin&section=XWiki.WatermarkAdmin`

### 5. 检查权限
确保当前用户有管理员权限：
- 使用Admin用户登录
- 或者给当前用户分配管理员权限

### 6. 清除缓存
- 重启XWiki服务器
- 或者访问 `http://localhost:8080/xwiki/bin/admin/XWiki/XWikiPreferences?editor=globaladmin&section=Cache` 清除缓存

## 替代访问方式

如果管理界面集成仍有问题，可以通过以下方式配置水印：

### 方式1：直接编辑XWikiPreferences
1. 访问 `http://localhost:8080/xwiki/bin/edit/XWiki/XWikiPreferences?editor=object`
2. 找到 XWiki.XWikiPreferences 对象
3. 添加以下属性：
   - `watermark.enabled = 1`
   - `watermark.textTemplate = ${user} - ${timestamp}`
   - `watermark.xSpacing = 200`
   - `watermark.ySpacing = 100`
   - `watermark.angle = -30`
   - `watermark.opacity = 0.1`
   - `watermark.fontSize = 14`
   - `watermark.antiCopy = 0`
   - `watermark.applyToMobile = 1`

### 方式2：通过Velocity脚本配置
创建一个页面，使用以下Velocity脚本：

```velocity
{{velocity}}
#set ($prefDoc = $xwiki.getDocument('XWiki.XWikiPreferences'))
#set ($prefObj = $prefDoc.getObject('XWiki.XWikiPreferences'))
#if (!$prefObj)
  #set ($prefObj = $prefDoc.newObject('XWiki.XWikiPreferences'))
#end

$prefObj.set('watermark.enabled', '1')
$prefObj.set('watermark.textTemplate', '${user} - ${timestamp}')
$prefObj.set('watermark.xSpacing', '200')
$prefObj.set('watermark.ySpacing', '100')
$prefObj.set('watermark.angle', '-30')
$prefObj.set('watermark.opacity', '0.1')
$prefObj.set('watermark.fontSize', '14')
$prefObj.set('watermark.antiCopy', '0')
$prefObj.set('watermark.applyToMobile', '1')

$prefDoc.save('Configured watermark settings')

Watermark configuration updated!
{{/velocity}}
```

## 验证水印功能

配置完成后，访问任何页面应该能看到水印效果。如果没有看到：

1. 检查浏览器控制台是否有JavaScript错误
2. 确认水印已启用（`watermark.enabled = 1`）
3. 检查Skin扩展是否正确加载

## 技术说明

管理界面集成使用了XWiki的AdminSheetClass机制：
- 页面包含AdminSheetClass对象，section设置为"Other"
- 这会将页面添加到管理界面的"其他"分类下
- 页面标题使用国际化key `admin.watermark`

如果仍有问题，请检查XWiki日志文件获取更多错误信息。
