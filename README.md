# XWiki 水印扩展

一个为 XWiki 17.4.3 设计的专业水印扩展，支持动态文本水印、占位符渲染、防复制功能和移动端兼容性。

## 功能特性

### 🎨 核心功能
- **动态水印渲染**: 基于 HTML5 Canvas 的高质量水印生成
- **占位符支持**: 支持 `${user}` 和 `${timestamp}` 占位符自动替换
  - `${user}`: 当前登录用户名（未登录显示 "Anonymous user"）
  - `${timestamp}`: 当前时间戳（格式：yyyy-MM-dd HH:mm:ss）
- **灵活配置**: 可配置水印文本、样式、位置、透明度等参数
- **防复制保护**: 可选的内容保护功能，防止选择和复制
- **移动端适配**: 响应式设计，支持移动设备

### 🔧 技术特性
- **组件化架构**: 使用 XWiki 组件系统（@Component @Singleton）
- **自动初始化**: 安装时自动创建配置页面和 Skin 扩展
- **线程安全**: 支持并发访问，读写锁保护缓存
- **多语言支持**: 中文/英文自动切换

## 系统要求

- XWiki 17.4.3+
- Java 17+
- Maven 3.6+

## 安装方法

### 1. 构建扩展

```bash
# 克隆项目
git clone <repository-url>
cd xwiki-extension-watermark

# 构建 JAR 包
./gradlew build

# 生成的 JAR 文件位于 build/libs/ 目录
```

### 2. 安装到 XWiki

1. 将生成的 JAR 文件复制到 XWiki 的 `WEB-INF/lib/` 目录
2. 重启 XWiki 服务器
3. 扩展将自动初始化并创建必要的配置页面

### 3. 配置水印

1. 访问 `http://localhost:8080/bin/admin/XWiki/XWikiPreferences`
2. 在左侧导航栏找到 **"水印配置"** 分类下的 **"水印"**
   - 英文界面：**"Watermark Settings"** → **"Watermark"**
   - 中文界面：**"水印配置"** → **"水印"**
3. 勾选 **"启用水印"** 选项
4. 根据需要调整配置参数
5. 点击 **"保存配置"** 按钮

## 配置参数

| 参数名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| enabled | boolean | 是否启用水印 | false |
| textTemplate | string | 水印文本模板 | "${user} - ${timestamp}" |
| xSpacing | integer | 水平间距（像素） | 200 |
| ySpacing | integer | 垂直间距（像素） | 100 |
| angle | integer | 旋转角度（度） | -30 |
| opacity | float | 透明度（0.0-1.0） | 0.1 |
| fontSize | integer | 字体大小（像素） | 14 |
| antiCopy | boolean | 是否启用防复制 | false |
| applyToMobile | boolean | 是否应用到移动设备 | true |

## 使用示例

### 基本配置
```
启用水印: ✓
水印文本模板: ${user} - ${timestamp}
水平间距: 200px
垂直间距: 100px
旋转角度: -30°
透明度: 0.1
字体大小: 14px
```

### 自定义文本模板
```
公司内部文档 - ${user}
机密文档 - ${timestamp}
${user} 于 ${timestamp} 访问
```

### 防复制模式
启用防复制功能后，页面将：
- 禁用文本选择
- 禁用右键菜单
- 禁用常用快捷键（Ctrl+C, Ctrl+A, Ctrl+V 等）
- 禁用 F12 开发者工具

## 开发指南

### 项目结构
```
src/
├── main/
│   ├── java/
│   │   └── com/microcredchina/xwikiwatermark/
│   │       ├── WatermarkConfiguration.java          # 配置数据类
│   │       ├── WatermarkConfigurationService.java   # 配置服务
│   │       ├── WatermarkManager.java                # 核心管理器
│   │       └── WatermarkExtensionInitializer.java   # 扩展初始化器
│   └── resources/
│       ├── META-INF/
│       │   └── components.txt                       # 组件注册
│       ├── XWiki/
│       │   ├── WatermarkAdmin.xml                   # 管理界面
│       │   ├── WatermarkAdminSection.xml            # 管理分类配置
│       │   ├── WatermarkAdminTranslations.xml       # 英文翻译文档
│       │   ├── WatermarkAdminTranslations_zh.xml    # 中文翻译文档
│       │   └── WatermarkSkinExtension.xml           # Skin 扩展
│       ├── ApplicationResources.properties          # 英文翻译
│       ├── ApplicationResources_zh.properties       # 中文翻译
│       ├── watermark.js                            # 前端脚本
│       └── watermark.css                           # 样式文件
└── test/
    └── java/
        └── com/microcredchina/xwikiwatermark/
            ├── WatermarkConfigurationTest.java      # 配置测试
            └── WatermarkManagerTest.java            # 管理器测试
```

### 核心组件

#### WatermarkManager
核心管理组件，提供：
- 配置管理
- 占位符解析
- JSON 配置输出
- 移动设备检测

#### WatermarkConfigurationService
配置服务组件，提供：
- 从 XWikiPreferences 读取配置
- 配置缓存管理
- 线程安全访问

#### 前端渲染
- 基于 HTML5 Canvas 的水印渲染
- 响应式设计支持
- 防复制功能实现

### 运行测试

```bash
# 运行所有测试
./gradlew test

# 运行特定测试
./gradlew test --tests WatermarkConfigurationTest

# 生成测试报告
./gradlew test jacocoTestReport
```

## 故障排除

### 常见问题

1. **水印不显示**
   - 检查是否已启用水印功能
   - 确认浏览器支持 HTML5 Canvas
   - 检查控制台是否有 JavaScript 错误

2. **配置不生效**
   - 清除浏览器缓存
   - 重启 XWiki 服务器
   - 检查配置是否正确保存

3. **移动端显示异常**
   - 确认已启用移动端支持
   - 检查设备屏幕尺寸适配

4. **管理界面不显示**
   - 检查JAR文件是否正确安装到 `WEB-INF/lib/` 目录
   - 重启XWiki服务器
   - 直接访问：`http://localhost:8080/xwiki/bin/admin/XWiki/XWikiPreferences?editor=globaladmin&section=XWiki.WatermarkAdmin`
   - 检查是否有管理员权限

### 调试模式

在浏览器控制台中使用以下命令进行调试：

```javascript
// 查看当前配置
console.log(XWiki.watermarkConfig);

// 手动重新渲染水印
XWikiWatermark.renderWatermark();

// 更新配置
XWikiWatermark.updateConfig(newConfig);
```

## 许可证

本项目采用 LGPL 2.1+ 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.1
- 🔧 修复国际化问题
- 中文properties文件转换为Unicode编码（解决乱码问题）
- 修复管理界面分类显示问题
- 添加XWiki标准的翻译文档支持
- 优化管理界面的多语言切换

### v1.0.0
- 初始版本发布
- 支持基本水印功能
- 管理界面集成
- 多语言支持
- 移动端适配

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>
