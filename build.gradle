plugins {
    id 'java'
    id 'maven-publish'
}

group = 'com.microcredchina.xwikiwatermark'
version = '1.0-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

repositories {
    mavenLocal()
    mavenCentral()
    maven {
        url 'https://nexus.xwiki.org/nexus/content/groups/public/'
    }
    maven {
        url 'https://maven.xwiki.org/releases/'
    }
}

dependencies {
    // XWiki Core Dependencies (compileOnly - provided by XWiki runtime)
    compileOnly 'org.xwiki.commons:xwiki-commons-component-api:17.4.3'
    compileOnly 'org.xwiki.commons:xwiki-commons-script:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-oldcore:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-model-api:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-bridge:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-configuration-default:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-skin-skinx:17.4.3'
    compileOnly 'org.xwiki.platform:xwiki-platform-administration-ui:17.4.3'
    compileOnly 'org.xwiki.rendering:xwiki-rendering-api:17.4.3'

    // Servlet API (provided by container)
    compileOnly 'javax.servlet:javax.servlet-api:4.0.1'

    // SLF4J for logging (provided by XWiki)
    compileOnly 'org.slf4j:slf4j-api:1.7.36'

    // JSR-330 Dependency Injection
    compileOnly 'javax.inject:javax.inject:1'

    // Test Dependencies
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.mockito:mockito-core:5.7.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:5.7.0'

    // XWiki dependencies for testing
    testImplementation 'org.xwiki.commons:xwiki-commons-component-api:17.4.3'
    testImplementation 'org.xwiki.platform:xwiki-platform-model-api:17.4.3'
    testImplementation 'org.xwiki.platform:xwiki-platform-bridge:17.4.3'
}

test {
    useJUnitPlatform()
}

// JAR configuration for XWiki extension
jar {
    manifest {
        attributes(
            'Implementation-Title': 'XWiki Watermark Extension',
            'Implementation-Version': project.version,
            'Implementation-Vendor': 'MicroCredChina',
            'XWiki-Extension-Id': 'com.microcredchina.xwikiwatermark:xwiki-extension-watermark',
            'XWiki-Extension-Name': 'XWiki Watermark Extension',
            'XWiki-Extension-Description': 'Professional watermark extension for XWiki with dynamic text, placeholders, and anti-copy features',
            'XWiki-Extension-Version': project.version,
            'XWiki-Extension-Type': 'jar',
            'XWiki-Extension-Category': 'application'
        )
    }
}