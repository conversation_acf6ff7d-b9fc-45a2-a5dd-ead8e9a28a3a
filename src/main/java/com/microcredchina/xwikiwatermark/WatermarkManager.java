package com.microcredchina.xwikiwatermark;

import org.xwiki.component.annotation.Component;
import org.xwiki.component.annotation.Role;
import org.xwiki.bridge.DocumentAccessBridge;
import org.xwiki.model.reference.DocumentReference;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Core watermark management component.
 * Handles watermark configuration, placeholder resolution, and client-side integration.
 */
@Component
@Role
@Singleton
public class WatermarkManager {
    
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");
    private static final String ANONYMOUS_USER = "Anonymous user";
    private static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    @Inject
    private WatermarkConfigurationService configurationService;
    
    @Inject
    private DocumentAccessBridge documentAccessBridge;
    
    /**
     * Check if watermark is enabled for the current context.
     * 
     * @return true if watermark should be displayed
     */
    public boolean isWatermarkEnabled() {
        try {
            WatermarkConfiguration config = configurationService.getConfiguration();
            return config.isEnabled();
        } catch (Exception e) {
            // Log error and default to disabled
            return false;
        }
    }
    
    /**
     * Get the current watermark configuration.
     * 
     * @return Current watermark configuration
     */
    public WatermarkConfiguration getConfiguration() {
        return configurationService.getConfiguration();
    }
    
    /**
     * Resolve placeholders in the watermark text template.
     * Supports ${user} and ${timestamp} placeholders.
     * 
     * @param template The text template with placeholders
     * @return Resolved text with actual values
     */
    public String resolvePlaceholders(String template) {
        if (template == null || template.trim().isEmpty()) {
            return "";
        }
        
        Map<String, String> placeholders = createPlaceholderMap();
        
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String placeholderName = matcher.group(1);
            String replacement = placeholders.getOrDefault(placeholderName, matcher.group(0));
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * Get watermark configuration as JSON string for client-side use.
     * 
     * @return JSON representation of watermark configuration
     */
    public String getConfigurationAsJson() {
        try {
            WatermarkConfiguration config = getConfiguration();
            String resolvedText = resolvePlaceholders(config.getTextTemplate());
            
            StringBuilder json = new StringBuilder();
            json.append("{");
            json.append("\"enabled\":").append(config.isEnabled()).append(",");
            json.append("\"text\":\"").append(escapeJson(resolvedText)).append("\",");
            json.append("\"xSpacing\":").append(config.getXSpacing()).append(",");
            json.append("\"ySpacing\":").append(config.getYSpacing()).append(",");
            json.append("\"angle\":").append(config.getAngle()).append(",");
            json.append("\"opacity\":").append(config.getOpacity()).append(",");
            json.append("\"fontSize\":").append(config.getFontSize()).append(",");
            json.append("\"antiCopy\":").append(config.isAntiCopy()).append(",");
            json.append("\"applyToMobile\":").append(config.isApplyToMobile());
            json.append("}");
            
            return json.toString();
        } catch (Exception e) {
            // Return default disabled configuration
            return "{\"enabled\":false}";
        }
    }
    
    /**
     * Check if the current request is from a mobile device.
     * This is a simplified implementation - in production you might want
     * to use a more sophisticated user agent detection library.
     * 
     * @param userAgent The user agent string
     * @return true if the request appears to be from a mobile device
     */
    public boolean isMobileDevice(String userAgent) {
        if (userAgent == null) {
            return false;
        }
        
        String ua = userAgent.toLowerCase();
        return ua.contains("mobile") || 
               ua.contains("android") || 
               ua.contains("iphone") || 
               ua.contains("ipad") || 
               ua.contains("ipod") || 
               ua.contains("blackberry") || 
               ua.contains("windows phone");
    }
    
    /**
     * Create a map of placeholder values for the current context.
     * 
     * @return Map of placeholder names to their resolved values
     */
    private Map<String, String> createPlaceholderMap() {
        Map<String, String> placeholders = new HashMap<>();
        
        // Resolve user placeholder
        try {
            DocumentReference currentUser = documentAccessBridge.getCurrentUserReference();
            if (currentUser != null) {
                placeholders.put("user", currentUser.getName());
            } else {
                placeholders.put("user", ANONYMOUS_USER);
            }
        } catch (Exception e) {
            placeholders.put("user", ANONYMOUS_USER);
        }
        
        // Resolve timestamp placeholder
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(TIMESTAMP_FORMAT);
            placeholders.put("timestamp", dateFormat.format(new Date()));
        } catch (Exception e) {
            placeholders.put("timestamp", "");
        }
        
        return placeholders;
    }
    
    /**
     * Escape special characters for JSON string values.
     * 
     * @param text The text to escape
     * @return Escaped text safe for JSON
     */
    private String escapeJson(String text) {
        if (text == null) {
            return "";
        }
        
        return text.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }
    
    /**
     * Clear the configuration cache.
     * Useful when configuration has been updated.
     */
    public void clearConfigurationCache() {
        configurationService.clearCache();
    }
}
