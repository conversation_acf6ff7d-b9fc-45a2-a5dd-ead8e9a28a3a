package com.microcredchina.xwikiwatermark;

import org.xwiki.component.annotation.Component;
import org.xwiki.component.annotation.InstantiationStrategy;
import org.xwiki.component.descriptor.ComponentInstantiationStrategy;
import org.xwiki.extension.ExtensionId;
import org.xwiki.extension.InstalledExtension;
import org.xwiki.extension.event.ExtensionInstalledEvent;
import org.xwiki.extension.event.ExtensionUpgradedEvent;
import org.xwiki.extension.repository.InstalledExtensionRepository;
import org.xwiki.model.reference.DocumentReference;
import org.xwiki.model.reference.LocalDocumentReference;
import org.xwiki.observation.AbstractEventListener;
import org.xwiki.observation.event.Event;

import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;
import java.util.Arrays;
import java.util.List;

/**
 * Extension initializer that sets up the watermark system when the extension is installed or upgraded.
 * This ensures that the necessary configuration pages and skin extensions are properly initialized.
 */
@Component
@Named("WatermarkExtensionInitializer")
@Singleton
@InstantiationStrategy(ComponentInstantiationStrategy.SINGLETON)
public class WatermarkExtensionInitializer extends AbstractEventListener {
    
    private static final String EXTENSION_ID = "com.microcredchina.xwikiwatermark:xwiki-extension-watermark";
    
    @Inject
    private InstalledExtensionRepository installedExtensionRepository;
    
    /**
     * Default constructor.
     */
    public WatermarkExtensionInitializer() {
        super("WatermarkExtensionInitializer", 
              Arrays.asList(new ExtensionInstalledEvent(), new ExtensionUpgradedEvent()));
    }
    
    @Override
    public void onEvent(Event event, Object source, Object data) {
        ExtensionId extensionId = null;
        
        if (event instanceof ExtensionInstalledEvent) {
            ExtensionInstalledEvent installedEvent = (ExtensionInstalledEvent) event;
            extensionId = installedEvent.getExtensionId();
        } else if (event instanceof ExtensionUpgradedEvent) {
            ExtensionUpgradedEvent upgradedEvent = (ExtensionUpgradedEvent) event;
            extensionId = upgradedEvent.getExtensionId();
        }
        
        if (extensionId != null && EXTENSION_ID.equals(extensionId.getId())) {
            initializeWatermarkExtension();
        }
    }
    
    /**
     * Initialize the watermark extension by setting up necessary components.
     */
    private void initializeWatermarkExtension() {
        try {
            // Log initialization
            System.out.println("Initializing XWiki Watermark Extension...");
            
            // The extension initialization is handled by XWiki's document import system
            // The XML files in src/main/resources will be automatically imported
            
            // Additional initialization logic can be added here if needed
            // For example: creating default configuration, setting up permissions, etc.
            
            System.out.println("XWiki Watermark Extension initialized successfully.");
            
        } catch (Exception e) {
            System.err.println("Failed to initialize XWiki Watermark Extension: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Check if the watermark extension is installed.
     * 
     * @return true if the extension is installed
     */
    public boolean isWatermarkExtensionInstalled() {
        try {
            InstalledExtension extension = installedExtensionRepository.getInstalledExtension(EXTENSION_ID, null);
            return extension != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get the installed watermark extension.
     * 
     * @return the installed extension or null if not found
     */
    public InstalledExtension getWatermarkExtension() {
        try {
            return installedExtensionRepository.getInstalledExtension(EXTENSION_ID, null);
        } catch (Exception e) {
            return null;
        }
    }
}
