package com.microcredchina.xwikiwatermark;

/**
 * Watermark configuration data class.
 * Contains all configurable parameters for the watermark system.
 */
public class WatermarkConfiguration {
    
    // Default configuration values
    public static final boolean DEFAULT_ENABLED = false;
    public static final String DEFAULT_TEXT_TEMPLATE = "${user} - ${timestamp}";
    public static final int DEFAULT_X_SPACING = 200;
    public static final int DEFAULT_Y_SPACING = 100;
    public static final int DEFAULT_ANGLE = -30;
    public static final float DEFAULT_OPACITY = 0.1f;
    public static final int DEFAULT_FONT_SIZE = 14;
    public static final boolean DEFAULT_ANTI_COPY = false;
    public static final boolean DEFAULT_APPLY_TO_MOBILE = true;
    
    private boolean enabled;
    private String textTemplate;
    private int xSpacing;
    private int ySpacing;
    private int angle;
    private float opacity;
    private int fontSize;
    private boolean antiCopy;
    private boolean applyToMobile;
    
    public WatermarkConfiguration() {
        this.enabled = DEFAULT_ENABLED;
        this.textTemplate = DEFAULT_TEXT_TEMPLATE;
        this.xSpacing = DEFAULT_X_SPACING;
        this.ySpacing = DEFAULT_Y_SPACING;
        this.angle = DEFAULT_ANGLE;
        this.opacity = DEFAULT_OPACITY;
        this.fontSize = DEFAULT_FONT_SIZE;
        this.antiCopy = DEFAULT_ANTI_COPY;
        this.applyToMobile = DEFAULT_APPLY_TO_MOBILE;
    }
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getTextTemplate() {
        return textTemplate;
    }
    
    public void setTextTemplate(String textTemplate) {
        this.textTemplate = textTemplate != null ? textTemplate : DEFAULT_TEXT_TEMPLATE;
    }
    
    public int getXSpacing() {
        return xSpacing;
    }
    
    public void setXSpacing(int xSpacing) {
        this.xSpacing = xSpacing > 0 ? xSpacing : DEFAULT_X_SPACING;
    }
    
    public int getYSpacing() {
        return ySpacing;
    }
    
    public void setYSpacing(int ySpacing) {
        this.ySpacing = ySpacing > 0 ? ySpacing : DEFAULT_Y_SPACING;
    }
    
    public int getAngle() {
        return angle;
    }
    
    public void setAngle(int angle) {
        this.angle = angle;
    }
    
    public float getOpacity() {
        return opacity;
    }
    
    public void setOpacity(float opacity) {
        this.opacity = opacity >= 0.0f && opacity <= 1.0f ? opacity : DEFAULT_OPACITY;
    }
    
    public int getFontSize() {
        return fontSize;
    }
    
    public void setFontSize(int fontSize) {
        this.fontSize = fontSize > 0 ? fontSize : DEFAULT_FONT_SIZE;
    }
    
    public boolean isAntiCopy() {
        return antiCopy;
    }
    
    public void setAntiCopy(boolean antiCopy) {
        this.antiCopy = antiCopy;
    }
    
    public boolean isApplyToMobile() {
        return applyToMobile;
    }
    
    public void setApplyToMobile(boolean applyToMobile) {
        this.applyToMobile = applyToMobile;
    }
    
    @Override
    public String toString() {
        return "WatermarkConfiguration{" +
                "enabled=" + enabled +
                ", textTemplate='" + textTemplate + '\'' +
                ", xSpacing=" + xSpacing +
                ", ySpacing=" + ySpacing +
                ", angle=" + angle +
                ", opacity=" + opacity +
                ", fontSize=" + fontSize +
                ", antiCopy=" + antiCopy +
                ", applyToMobile=" + applyToMobile +
                '}';
    }
}
