package com.microcredchina.xwikiwatermark;

import org.xwiki.component.annotation.Component;
import org.xwiki.component.annotation.Role;
import org.xwiki.configuration.ConfigurationSource;
import org.xwiki.model.reference.DocumentReference;
import org.xwiki.model.reference.LocalDocumentReference;

import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Service for managing watermark configuration.
 * Reads configuration from XWikiPreferences and provides caching with thread safety.
 */
@Component
@Role
@Singleton
public class WatermarkConfigurationService {
    
    private static final String CONFIG_PREFIX = "watermark.";
    
    // Configuration keys
    private static final String KEY_ENABLED = CONFIG_PREFIX + "enabled";
    private static final String KEY_TEXT_TEMPLATE = CONFIG_PREFIX + "textTemplate";
    private static final String KEY_X_SPACING = CONFIG_PREFIX + "xSpacing";
    private static final String KEY_Y_SPACING = CONFIG_PREFIX + "ySpacing";
    private static final String KEY_ANGLE = CONFIG_PREFIX + "angle";
    private static final String KEY_OPACITY = CONFIG_PREFIX + "opacity";
    private static final String KEY_FONT_SIZE = CONFIG_PREFIX + "fontSize";
    private static final String KEY_ANTI_COPY = CONFIG_PREFIX + "antiCopy";
    private static final String KEY_APPLY_TO_MOBILE = CONFIG_PREFIX + "applyToMobile";
    
    @Inject
    @Named("wiki")
    private ConfigurationSource configurationSource;
    
    // Cache and thread safety
    private volatile WatermarkConfiguration cachedConfiguration;
    private volatile long lastCacheTime = 0;
    private static final long CACHE_DURATION_MS = 30000; // 30 seconds cache
    private final ReadWriteLock cacheLock = new ReentrantReadWriteLock();
    
    /**
     * Get the current watermark configuration.
     * Uses caching with thread-safe access.
     * 
     * @return Current watermark configuration
     */
    public WatermarkConfiguration getConfiguration() {
        cacheLock.readLock().lock();
        try {
            long currentTime = System.currentTimeMillis();
            if (cachedConfiguration != null && 
                (currentTime - lastCacheTime) < CACHE_DURATION_MS) {
                return cachedConfiguration;
            }
        } finally {
            cacheLock.readLock().unlock();
        }
        
        // Need to refresh cache
        cacheLock.writeLock().lock();
        try {
            // Double-check pattern
            long currentTime = System.currentTimeMillis();
            if (cachedConfiguration != null && 
                (currentTime - lastCacheTime) < CACHE_DURATION_MS) {
                return cachedConfiguration;
            }
            
            // Load fresh configuration
            cachedConfiguration = loadConfiguration();
            lastCacheTime = currentTime;
            return cachedConfiguration;
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * Clear the configuration cache.
     * Forces reload on next access.
     */
    public void clearCache() {
        cacheLock.writeLock().lock();
        try {
            cachedConfiguration = null;
            lastCacheTime = 0;
        } finally {
            cacheLock.writeLock().unlock();
        }
    }
    
    /**
     * Load configuration from XWikiPreferences.
     * 
     * @return Loaded configuration with defaults for missing values
     */
    private WatermarkConfiguration loadConfiguration() {
        WatermarkConfiguration config = new WatermarkConfiguration();
        
        try {
            // Load boolean values
            config.setEnabled(getBooleanProperty(KEY_ENABLED, WatermarkConfiguration.DEFAULT_ENABLED));
            config.setAntiCopy(getBooleanProperty(KEY_ANTI_COPY, WatermarkConfiguration.DEFAULT_ANTI_COPY));
            config.setApplyToMobile(getBooleanProperty(KEY_APPLY_TO_MOBILE, WatermarkConfiguration.DEFAULT_APPLY_TO_MOBILE));
            
            // Load string values
            config.setTextTemplate(getStringProperty(KEY_TEXT_TEMPLATE, WatermarkConfiguration.DEFAULT_TEXT_TEMPLATE));
            
            // Load integer values
            config.setXSpacing(getIntProperty(KEY_X_SPACING, WatermarkConfiguration.DEFAULT_X_SPACING));
            config.setYSpacing(getIntProperty(KEY_Y_SPACING, WatermarkConfiguration.DEFAULT_Y_SPACING));
            config.setAngle(getIntProperty(KEY_ANGLE, WatermarkConfiguration.DEFAULT_ANGLE));
            config.setFontSize(getIntProperty(KEY_FONT_SIZE, WatermarkConfiguration.DEFAULT_FONT_SIZE));
            
            // Load float values
            config.setOpacity(getFloatProperty(KEY_OPACITY, WatermarkConfiguration.DEFAULT_OPACITY));
            
        } catch (Exception e) {
            // Log error and return default configuration
            // In a real implementation, you would use proper logging
            System.err.println("Error loading watermark configuration: " + e.getMessage());
            return new WatermarkConfiguration();
        }
        
        return config;
    }
    
    private boolean getBooleanProperty(String key, boolean defaultValue) {
        try {
            String value = configurationSource.getProperty(key);
            return value != null ? Boolean.parseBoolean(value) : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    private String getStringProperty(String key, String defaultValue) {
        try {
            String value = configurationSource.getProperty(key);
            return value != null && !value.trim().isEmpty() ? value : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    private int getIntProperty(String key, int defaultValue) {
        try {
            String value = configurationSource.getProperty(key);
            return value != null ? Integer.parseInt(value) : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    private float getFloatProperty(String key, float defaultValue) {
        try {
            String value = configurationSource.getProperty(key);
            return value != null ? Float.parseFloat(value) : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
}
