/**
 * XWiki Watermark Extension - Styles
 * Provides responsive design and mobile compatibility
 */

/* Watermark canvas base styles */
#xwiki-watermark-canvas {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    z-index: 9999 !important;
    opacity: 0.1;
    transition: opacity 0.3s ease;
}

/* Ensure watermark doesn't interfere with page interactions */
#xwiki-watermark-canvas {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Admin interface styles */
.watermark-admin-form {
    max-width: 800px;
    margin: 0 auto;
}

.watermark-admin-form .form-group {
    margin-bottom: 20px;
}

.watermark-admin-form .help-block {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}

.watermark-admin-form .checkbox {
    margin-bottom: 10px;
}

.watermark-admin-form .checkbox input[type="checkbox"] {
    margin-right: 8px;
}

/* Preview area */
.watermark-preview {
    position: relative;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 40px 20px;
    margin: 20px 0;
    min-height: 200px;
    overflow: hidden;
}

.watermark-preview::before {
    content: attr(data-preview-text);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: repeating-linear-gradient(
        -30deg,
        transparent,
        transparent 35px,
        rgba(0, 0, 0, 0.1) 35px,
        rgba(0, 0, 0, 0.1) 70px
    );
    pointer-events: none;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(-30deg);
    white-space: nowrap;
}

.watermark-preview-content {
    position: relative;
    z-index: 1;
    background: white;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .watermark-admin-form {
        padding: 0 15px;
    }
    
    .watermark-admin-form .row {
        margin: 0 -5px;
    }
    
    .watermark-admin-form .row [class*="col-"] {
        padding: 0 5px;
        margin-bottom: 15px;
    }
    
    .watermark-preview {
        margin: 15px 0;
        padding: 20px 10px;
        min-height: 150px;
    }
    
    .watermark-preview::before {
        font-size: 12px;
    }
    
    /* Adjust watermark canvas for mobile */
    #xwiki-watermark-canvas {
        opacity: 0.08; /* Slightly more transparent on mobile */
    }
}

@media (max-width: 480px) {
    .watermark-admin-form .form-group {
        margin-bottom: 15px;
    }
    
    .watermark-admin-form input[type="text"],
    .watermark-admin-form input[type="number"] {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .watermark-preview {
        min-height: 120px;
        padding: 15px 8px;
    }
    
    .watermark-preview::before {
        font-size: 10px;
    }
}

/* High DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    #xwiki-watermark-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print styles - hide watermark when printing */
@media print {
    #xwiki-watermark-canvas {
        display: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .watermark-preview {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .watermark-preview-content {
        background: #1a202c;
        color: #e2e8f0;
    }
    
    .watermark-admin-form .help-block {
        color: #a0aec0;
    }
}

/* Accessibility improvements */
.watermark-admin-form label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.watermark-admin-form input:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.watermark-admin-form .checkbox label {
    font-weight: normal;
    cursor: pointer;
    display: flex;
    align-items: center;
}

/* Animation for watermark appearance */
@keyframes watermarkFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.1;
    }
}

#xwiki-watermark-canvas.fade-in {
    animation: watermarkFadeIn 0.5s ease-in-out;
}

/* Ensure watermark works with XWiki themes */
.xwiki-watermark-container {
    position: relative;
    z-index: 1;
}

/* Fix for potential conflicts with XWiki UI */
.xwiki-watermark-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 9999;
}

/* Loading state */
.watermark-loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.watermark-loaded {
    opacity: 0.1;
}
