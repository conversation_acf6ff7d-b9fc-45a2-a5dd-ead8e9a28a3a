<?xml version="1.0" encoding="UTF-8"?>

<!--
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
-->

<xwikidoc version="1.3">
  <web>XWiki</web>
  <name>WatermarkAdmin</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <creationDate>1640995200000</creationDate>
  <parent>XWiki.XWikiPreferences</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <date>1640995200000</date>
  <contentUpdateDate>1640995200000</contentUpdateDate>
  <version>1.1</version>
  <title>Watermark Settings</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <content>{{velocity}}
#if ($request.action == 'save')
  #set ($prefDoc = $xwiki.getDocument('XWiki.XWikiPreferences'))
  #set ($prefObj = $prefDoc.getObject('XWiki.XWikiPreferences'))
  #if (!$prefObj)
    #set ($prefObj = $prefDoc.newObject('XWiki.XWikiPreferences'))
  #end
  
  ## Save watermark configuration
  $prefObj.set('watermark.enabled', $request.get('watermark_enabled'))
  $prefObj.set('watermark.textTemplate', $request.get('watermark_textTemplate'))
  $prefObj.set('watermark.xSpacing', $request.get('watermark_xSpacing'))
  $prefObj.set('watermark.ySpacing', $request.get('watermark_ySpacing'))
  $prefObj.set('watermark.angle', $request.get('watermark_angle'))
  $prefObj.set('watermark.opacity', $request.get('watermark_opacity'))
  $prefObj.set('watermark.fontSize', $request.get('watermark_fontSize'))
  $prefObj.set('watermark.antiCopy', $request.get('watermark_antiCopy'))
  $prefObj.set('watermark.applyToMobile', $request.get('watermark_applyToMobile'))
  
  $prefDoc.save('Updated watermark configuration')
  
  #set ($message = $services.localization.render('watermark.admin.saved'))
  {{info}}$message{{/info}}
#end

## Load current configuration
#set ($prefDoc = $xwiki.getDocument('XWiki.XWikiPreferences'))
#set ($prefObj = $prefDoc.getObject('XWiki.XWikiPreferences'))

#set ($enabled = false)
#set ($textTemplate = '${user} - ${timestamp}')
#set ($xSpacing = 200)
#set ($ySpacing = 100)
#set ($angle = -30)
#set ($opacity = 0.1)
#set ($fontSize = 14)
#set ($antiCopy = false)
#set ($applyToMobile = true)

#if ($prefObj)
  #set ($enabled = $prefObj.getProperty('watermark.enabled').value)
  #set ($textTemplate = $prefObj.getProperty('watermark.textTemplate').value)
  #set ($xSpacing = $prefObj.getProperty('watermark.xSpacing').value)
  #set ($ySpacing = $prefObj.getProperty('watermark.ySpacing').value)
  #set ($angle = $prefObj.getProperty('watermark.angle').value)
  #set ($opacity = $prefObj.getProperty('watermark.opacity').value)
  #set ($fontSize = $prefObj.getProperty('watermark.fontSize').value)
  #set ($antiCopy = $prefObj.getProperty('watermark.antiCopy').value)
  #set ($applyToMobile = $prefObj.getProperty('watermark.applyToMobile').value)
#end

## Set default values if null
#if (!$textTemplate || $textTemplate == '')
  #set ($textTemplate = '${user} - ${timestamp}')
#end
#if (!$xSpacing || $xSpacing == '')
  #set ($xSpacing = 200)
#end
#if (!$ySpacing || $ySpacing == '')
  #set ($ySpacing = 100)
#end
#if (!$angle || $angle == '')
  #set ($angle = -30)
#end
#if (!$opacity || $opacity == '')
  #set ($opacity = 0.1)
#end
#if (!$fontSize || $fontSize == '')
  #set ($fontSize = 14)
#end

= $services.localization.render('watermark.admin.title') =

$services.localization.render('watermark.admin.description')

{{html}}
<form method="post" action="$doc.getURL('view')">
  <input type="hidden" name="action" value="save" />
  
  <div class="form-group">
    <label class="checkbox">
      <input type="checkbox" name="watermark_enabled" value="1" #if($enabled)checked="checked"#end />
      $services.localization.render('watermark.admin.enabled')
    </label>
    <div class="help-block">$services.localization.render('watermark.admin.enabled.hint')</div>
  </div>
  
  <div class="form-group">
    <label for="watermark_textTemplate">$services.localization.render('watermark.admin.textTemplate')</label>
    <input type="text" class="form-control" id="watermark_textTemplate" name="watermark_textTemplate" value="$!{textTemplate}" />
    <div class="help-block">$services.localization.render('watermark.admin.textTemplate.hint')</div>
  </div>
  
  <div class="row">
    <div class="col-md-6">
      <div class="form-group">
        <label for="watermark_xSpacing">$services.localization.render('watermark.admin.xSpacing')</label>
        <input type="number" class="form-control" id="watermark_xSpacing" name="watermark_xSpacing" value="$!{xSpacing}" min="50" max="1000" />
        <div class="help-block">$services.localization.render('watermark.admin.xSpacing.hint')</div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="form-group">
        <label for="watermark_ySpacing">$services.localization.render('watermark.admin.ySpacing')</label>
        <input type="number" class="form-control" id="watermark_ySpacing" name="watermark_ySpacing" value="$!{ySpacing}" min="50" max="1000" />
        <div class="help-block">$services.localization.render('watermark.admin.ySpacing.hint')</div>
      </div>
    </div>
  </div>
  
  <div class="row">
    <div class="col-md-4">
      <div class="form-group">
        <label for="watermark_angle">$services.localization.render('watermark.admin.angle')</label>
        <input type="number" class="form-control" id="watermark_angle" name="watermark_angle" value="$!{angle}" min="-90" max="90" />
        <div class="help-block">$services.localization.render('watermark.admin.angle.hint')</div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        <label for="watermark_opacity">$services.localization.render('watermark.admin.opacity')</label>
        <input type="number" class="form-control" id="watermark_opacity" name="watermark_opacity" value="$!{opacity}" min="0.01" max="1.0" step="0.01" />
        <div class="help-block">$services.localization.render('watermark.admin.opacity.hint')</div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="form-group">
        <label for="watermark_fontSize">$services.localization.render('watermark.admin.fontSize')</label>
        <input type="number" class="form-control" id="watermark_fontSize" name="watermark_fontSize" value="$!{fontSize}" min="8" max="48" />
        <div class="help-block">$services.localization.render('watermark.admin.fontSize.hint')</div>
      </div>
    </div>
  </div>
  
  <div class="form-group">
    <label class="checkbox">
      <input type="checkbox" name="watermark_antiCopy" value="1" #if($antiCopy)checked="checked"#end />
      $services.localization.render('watermark.admin.antiCopy')
    </label>
    <div class="help-block">$services.localization.render('watermark.admin.antiCopy.hint')</div>
  </div>
  
  <div class="form-group">
    <label class="checkbox">
      <input type="checkbox" name="watermark_applyToMobile" value="1" #if($applyToMobile)checked="checked"#end />
      $services.localization.render('watermark.admin.applyToMobile')
    </label>
    <div class="help-block">$services.localization.render('watermark.admin.applyToMobile.hint')</div>
  </div>
  
  <div class="form-group">
    <button type="submit" class="btn btn-primary">$services.localization.render('watermark.admin.save')</button>
  </div>
</form>
{{/html}}
{{/velocity}}</content>
  <object>
    <name>XWiki.AdminSheetClass</name>
    <number>0</number>
    <className>XWiki.AdminSheetClass</className>
    <guid>12345678-1234-1234-1234-123456789012</guid>
    <class>
      <name>XWiki.AdminSheetClass</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <section>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>section</name>
        <number>1</number>
        <prettyName>Section</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>Watermark Settings</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </section>
    </class>
    <property>
      <section>Watermark Settings</section>
    </property>
  </object>
</xwikidoc>
