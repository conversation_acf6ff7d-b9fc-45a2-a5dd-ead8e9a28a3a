<?xml version="1.0" encoding="UTF-8"?>

<!--
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
-->

<xwikidoc version="1.3">
  <web>XWiki</web>
  <name>WatermarkSkinExtension</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <creationDate>1640995200000</creationDate>
  <parent>XWiki.XWikiPreferences</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <date>1640995200000</date>
  <contentUpdateDate>1640995200000</contentUpdateDate>
  <version>1.1</version>
  <title>Watermark Skin Extension</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <content>{{velocity}}
#set ($watermarkManager = $services.component.getInstance('com.microcredchina.xwikiwatermark.WatermarkManager'))
#if ($watermarkManager && $watermarkManager.isWatermarkEnabled())
  #set ($watermarkConfig = $watermarkManager.getConfigurationAsJson())
  
  ## Include CSS
  $xwiki.ssx.use('XWiki.WatermarkSkinExtension', {'colorTheme': "$!{colorTheme}"})##
  
  ## Include JavaScript with configuration
  $xwiki.jsx.use('XWiki.WatermarkSkinExtension', {'colorTheme': "$!{colorTheme}"})##
  
  ## Inject watermark configuration into page
  <script type="text/javascript">
  //<![CDATA[
  (function() {
    if (typeof XWiki === 'undefined') {
      window.XWiki = {};
    }
    XWiki.watermarkConfig = $watermarkConfig;
  })();
  //]]>
  </script>
#end
{{/velocity}}</content>
  <object>
    <name>XWiki.StyleSheetExtension</name>
    <number>0</number>
    <className>XWiki.StyleSheetExtension</className>
    <guid>12345678-1234-1234-1234-123456789013</guid>
    <class>
      <name>XWiki.StyleSheetExtension</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <cache>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>cache</name>
        <number>1</number>
        <prettyName>Caching policy</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>long|short|default|forbid</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </cache>
      <code>
        <contenttype>PureText</contenttype>
        <disabled>0</disabled>
        <editor>PureText</editor>
        <name>code</name>
        <number>2</number>
        <prettyName>Code</prettyName>
        <rows>20</rows>
        <size>50</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.TextAreaClass</classType>
      </code>
      <contentType>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>contentType</name>
        <number>3</number>
        <prettyName>Content Type</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>CSS|LESS</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </contentType>
      <name>
        <disabled>0</disabled>
        <name>name</name>
        <number>4</number>
        <prettyName>Name</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
      </name>
      <parse>
        <disabled>0</disabled>
        <displayFormType>select</displayFormType>
        <displayType>yesno</displayType>
        <name>parse</name>
        <number>5</number>
        <prettyName>Parse content</prettyName>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      </parse>
      <use>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>use</name>
        <number>6</number>
        <prettyName>Use this extension</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>currentPage|onDemand|always</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </use>
    </class>
    <property>
      <cache>long</cache>
    </property>
    <property>
      <code>/**
 * XWiki Watermark Extension - Styles
 * Provides responsive design and mobile compatibility
 */

/* Watermark canvas base styles */
#xwiki-watermark-canvas {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    z-index: 9999 !important;
    opacity: 0.1;
    transition: opacity 0.3s ease;
}

/* Ensure watermark doesn't interfere with page interactions */
#xwiki-watermark-canvas {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    /* Adjust watermark canvas for mobile */
    #xwiki-watermark-canvas {
        opacity: 0.08; /* Slightly more transparent on mobile */
    }
}

/* High DPI display support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    #xwiki-watermark-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print styles - hide watermark when printing */
@media print {
    #xwiki-watermark-canvas {
        display: none !important;
    }
}

/* Animation for watermark appearance */
@keyframes watermarkFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 0.1;
    }
}

#xwiki-watermark-canvas.fade-in {
    animation: watermarkFadeIn 0.5s ease-in-out;
}

/* Loading state */
.watermark-loading {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.watermark-loaded {
    opacity: 0.1;
}</code>
    </property>
    <property>
      <contentType>CSS</contentType>
    </property>
    <property>
      <name>Watermark CSS</name>
    </property>
    <property>
      <parse>1</parse>
    </property>
    <property>
      <use>always</use>
    </property>
  </object>
  <object>
    <name>XWiki.JavaScriptExtension</name>
    <number>0</number>
    <className>XWiki.JavaScriptExtension</className>
    <guid>12345678-1234-1234-1234-123456789014</guid>
    <class>
      <name>XWiki.JavaScriptExtension</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <cache>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>cache</name>
        <number>1</number>
        <prettyName>Caching policy</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>long|short|default|forbid</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </cache>
      <code>
        <contenttype>PureText</contenttype>
        <disabled>0</disabled>
        <editor>PureText</editor>
        <name>code</name>
        <number>2</number>
        <prettyName>Code</prettyName>
        <rows>20</rows>
        <size>50</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.TextAreaClass</classType>
      </code>
      <name>
        <disabled>0</disabled>
        <name>name</name>
        <number>3</number>
        <prettyName>Name</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
      </name>
      <parse>
        <disabled>0</disabled>
        <displayFormType>select</displayFormType>
        <displayType>yesno</displayType>
        <name>parse</name>
        <number>4</number>
        <prettyName>Parse content</prettyName>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      </parse>
      <use>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>use</name>
        <number>5</number>
        <prettyName>Use this extension</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>currentPage|onDemand|always</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </use>
    </class>
    <property>
      <cache>long</cache>
    </property>
    <property>
      <code>/**
 * XWiki Watermark Extension - Client-side watermark rendering
 * Provides dynamic watermark generation using HTML5 Canvas
 */
(function() {
    'use strict';
    
    var WatermarkRenderer = {
        canvas: null,
        config: null,
        initialized: false,
        
        /**
         * Initialize the watermark system
         */
        init: function(configuration) {
            if (this.initialized) {
                return;
            }
            
            this.config = configuration || {};
            
            // Check if watermark is enabled
            if (!this.config.enabled) {
                return;
            }
            
            // Check mobile device support
            if (this.isMobileDevice() && !this.config.applyToMobile) {
                return;
            }
            
            this.createWatermarkCanvas();
            this.renderWatermark();
            
            if (this.config.antiCopy) {
                this.enableAntiCopy();
            }
            
            this.initialized = true;
            
            // Re-render on window resize
            var self = this;
            window.addEventListener('resize', function() {
                setTimeout(function() {
                    self.updateCanvasSize();
                    self.renderWatermark();
                }, 100);
            });
        },
        
        /**
         * Create the watermark canvas element
         */
        createWatermarkCanvas: function() {
            // Remove existing canvas if any
            var existingCanvas = document.getElementById('xwiki-watermark-canvas');
            if (existingCanvas) {
                existingCanvas.remove();
            }
            
            this.canvas = document.createElement('canvas');
            this.canvas.id = 'xwiki-watermark-canvas';
            this.canvas.style.cssText = [
                'position: fixed',
                'top: 0',
                'left: 0',
                'width: 100%',
                'height: 100%',
                'pointer-events: none',
                'z-index: 9999',
                'opacity: ' + (this.config.opacity || 0.1)
            ].join('; ');
            
            this.updateCanvasSize();
            document.body.appendChild(this.canvas);
        },
        
        /**
         * Update canvas size to match viewport
         */
        updateCanvasSize: function() {
            if (!this.canvas) return;
            
            var dpr = window.devicePixelRatio || 1;
            var rect = this.canvas.getBoundingClientRect();
            
            this.canvas.width = rect.width * dpr;
            this.canvas.height = rect.height * dpr;
            
            var ctx = this.canvas.getContext('2d');
            ctx.scale(dpr, dpr);
        },
        
        /**
         * Render the watermark pattern
         */
        renderWatermark: function() {
            if (!this.canvas) return;
            
            var ctx = this.canvas.getContext('2d');
            var rect = this.canvas.getBoundingClientRect();
            
            // Clear canvas
            ctx.clearRect(0, 0, rect.width, rect.height);
            
            // Set font and style
            ctx.font = (this.config.fontSize || 14) + 'px Arial, sans-serif';
            ctx.fillStyle = '#000000';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            var text = this.config.text || 'Watermark';
            var xSpacing = this.config.xSpacing || 200;
            var ySpacing = this.config.ySpacing || 100;
            var angle = (this.config.angle || -30) * Math.PI / 180;
            
            // Calculate grid dimensions
            var cols = Math.ceil(rect.width / xSpacing) + 2;
            var rows = Math.ceil(rect.height / ySpacing) + 2;
            
            // Render watermark grid
            for (var row = 0; row < rows; row++) {
                for (var col = 0; col < cols; col++) {
                    var x = col * xSpacing - xSpacing / 2;
                    var y = row * ySpacing - ySpacing / 2;
                    
                    // Offset alternate rows for better coverage
                    if (row % 2 === 1) {
                        x += xSpacing / 2;
                    }
                    
                    ctx.save();
                    ctx.translate(x, y);
                    ctx.rotate(angle);
                    ctx.fillText(text, 0, 0);
                    ctx.restore();
                }
            }
        },
        
        /**
         * Enable anti-copy protection
         */
        enableAntiCopy: function() {
            var style = document.createElement('style');
            style.textContent = [
                'body { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }',
                'body { -webkit-touch-callout: none; -webkit-tap-highlight-color: transparent; }',
                '::selection { background: transparent; }',
                '::-moz-selection { background: transparent; }'
            ].join('\n');
            document.head.appendChild(style);
            
            // Disable context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable text selection
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable drag
            document.addEventListener('dragstart', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable common keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Disable Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+S, Ctrl+P, F12
                if (e.ctrlKey && (e.keyCode === 65 || e.keyCode === 67 || e.keyCode === 86 || 
                                  e.keyCode === 88 || e.keyCode === 83 || e.keyCode === 80)) {
                    e.preventDefault();
                    return false;
                }
                
                // Disable F12 (Developer Tools)
                if (e.keyCode === 123) {
                    e.preventDefault();
                    return false;
                }
            });
        },
        
        /**
         * Check if current device is mobile
         */
        isMobileDevice: function() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   (window.innerWidth <= 768);
        },
        
        /**
         * Update watermark configuration
         */
        updateConfig: function(newConfig) {
            this.config = newConfig || {};
            if (this.initialized && this.config.enabled) {
                this.renderWatermark();
            } else if (!this.config.enabled && this.canvas) {
                this.canvas.remove();
                this.canvas = null;
                this.initialized = false;
            }
        },
        
        /**
         * Destroy watermark
         */
        destroy: function() {
            if (this.canvas) {
                this.canvas.remove();
                this.canvas = null;
            }
            this.initialized = false;
        }
    };
    
    // Auto-initialize when DOM is ready
    function initWatermark() {
        // Get configuration from XWiki context
        if (typeof XWiki !== 'undefined' && XWiki.watermarkConfig) {
            WatermarkRenderer.init(XWiki.watermarkConfig);
        }
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWatermark);
    } else {
        initWatermark();
    }
    
    // Export to global scope for external access
    window.XWikiWatermark = WatermarkRenderer;
    
})();</code>
    </property>
    <property>
      <name>Watermark JavaScript</name>
    </property>
    <property>
      <parse>1</parse>
    </property>
    <property>
      <use>always</use>
    </property>
  </object>
</xwikidoc>
