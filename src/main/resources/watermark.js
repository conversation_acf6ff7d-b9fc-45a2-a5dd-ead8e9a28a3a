/**
 * XWiki Watermark Extension - Client-side watermark rendering
 * Provides dynamic watermark generation using HTML5 Canvas
 */
(function() {
    'use strict';
    
    var WatermarkRenderer = {
        canvas: null,
        config: null,
        initialized: false,
        
        /**
         * Initialize the watermark system
         */
        init: function(configuration) {
            if (this.initialized) {
                return;
            }
            
            this.config = configuration || {};
            
            // Check if watermark is enabled
            if (!this.config.enabled) {
                return;
            }
            
            // Check mobile device support
            if (this.isMobileDevice() && !this.config.applyToMobile) {
                return;
            }
            
            this.createWatermarkCanvas();
            this.renderWatermark();
            
            if (this.config.antiCopy) {
                this.enableAntiCopy();
            }
            
            this.initialized = true;
            
            // Re-render on window resize
            var self = this;
            window.addEventListener('resize', function() {
                setTimeout(function() {
                    self.updateCanvasSize();
                    self.renderWatermark();
                }, 100);
            });
        },
        
        /**
         * Create the watermark canvas element
         */
        createWatermarkCanvas: function() {
            // Remove existing canvas if any
            var existingCanvas = document.getElementById('xwiki-watermark-canvas');
            if (existingCanvas) {
                existingCanvas.remove();
            }
            
            this.canvas = document.createElement('canvas');
            this.canvas.id = 'xwiki-watermark-canvas';
            this.canvas.style.cssText = [
                'position: fixed',
                'top: 0',
                'left: 0',
                'width: 100%',
                'height: 100%',
                'pointer-events: none',
                'z-index: 9999',
                'opacity: ' + (this.config.opacity || 0.1)
            ].join('; ');
            
            this.updateCanvasSize();
            document.body.appendChild(this.canvas);
        },
        
        /**
         * Update canvas size to match viewport
         */
        updateCanvasSize: function() {
            if (!this.canvas) return;
            
            var dpr = window.devicePixelRatio || 1;
            var rect = this.canvas.getBoundingClientRect();
            
            this.canvas.width = rect.width * dpr;
            this.canvas.height = rect.height * dpr;
            
            var ctx = this.canvas.getContext('2d');
            ctx.scale(dpr, dpr);
        },
        
        /**
         * Render the watermark pattern
         */
        renderWatermark: function() {
            if (!this.canvas) return;
            
            var ctx = this.canvas.getContext('2d');
            var rect = this.canvas.getBoundingClientRect();
            
            // Clear canvas
            ctx.clearRect(0, 0, rect.width, rect.height);
            
            // Set font and style
            ctx.font = (this.config.fontSize || 14) + 'px Arial, sans-serif';
            ctx.fillStyle = '#000000';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            var text = this.config.text || 'Watermark';
            var xSpacing = this.config.xSpacing || 200;
            var ySpacing = this.config.ySpacing || 100;
            var angle = (this.config.angle || -30) * Math.PI / 180;
            
            // Calculate text dimensions
            var textMetrics = ctx.measureText(text);
            var textWidth = textMetrics.width;
            var textHeight = this.config.fontSize || 14;
            
            // Calculate grid dimensions
            var cols = Math.ceil(rect.width / xSpacing) + 2;
            var rows = Math.ceil(rect.height / ySpacing) + 2;
            
            // Render watermark grid
            for (var row = 0; row < rows; row++) {
                for (var col = 0; col < cols; col++) {
                    var x = col * xSpacing - xSpacing / 2;
                    var y = row * ySpacing - ySpacing / 2;
                    
                    // Offset alternate rows for better coverage
                    if (row % 2 === 1) {
                        x += xSpacing / 2;
                    }
                    
                    ctx.save();
                    ctx.translate(x, y);
                    ctx.rotate(angle);
                    ctx.fillText(text, 0, 0);
                    ctx.restore();
                }
            }
        },
        
        /**
         * Enable anti-copy protection
         */
        enableAntiCopy: function() {
            var style = document.createElement('style');
            style.textContent = [
                'body { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }',
                'body { -webkit-touch-callout: none; -webkit-tap-highlight-color: transparent; }',
                '::selection { background: transparent; }',
                '::-moz-selection { background: transparent; }'
            ].join('\n');
            document.head.appendChild(style);
            
            // Disable context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable text selection
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable drag
            document.addEventListener('dragstart', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable common keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Disable Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+S, Ctrl+P, F12
                if (e.ctrlKey && (e.keyCode === 65 || e.keyCode === 67 || e.keyCode === 86 || 
                                  e.keyCode === 88 || e.keyCode === 83 || e.keyCode === 80)) {
                    e.preventDefault();
                    return false;
                }
                
                // Disable F12 (Developer Tools)
                if (e.keyCode === 123) {
                    e.preventDefault();
                    return false;
                }
            });
        },
        
        /**
         * Check if current device is mobile
         */
        isMobileDevice: function() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   (window.innerWidth <= 768);
        },
        
        /**
         * Update watermark configuration
         */
        updateConfig: function(newConfig) {
            this.config = newConfig || {};
            if (this.initialized && this.config.enabled) {
                this.renderWatermark();
            } else if (!this.config.enabled && this.canvas) {
                this.canvas.remove();
                this.canvas = null;
                this.initialized = false;
            }
        },
        
        /**
         * Destroy watermark
         */
        destroy: function() {
            if (this.canvas) {
                this.canvas.remove();
                this.canvas = null;
            }
            this.initialized = false;
        }
    };
    
    // Auto-initialize when DOM is ready
    function initWatermark() {
        // Try to get configuration from XWiki context
        if (typeof XWiki !== 'undefined' && XWiki.watermarkConfig) {
            WatermarkRenderer.init(XWiki.watermarkConfig);
        } else {
            // Fallback: try to load configuration via AJAX
            var xhr = new XMLHttpRequest();
            xhr.open('GET', XWiki.currentDocument.getURL('get', 'xpage=plain&outputSyntax=plain&sheet=XWiki.WatermarkConfigSheet'), true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        var config = JSON.parse(xhr.responseText);
                        WatermarkRenderer.init(config);
                    } catch (e) {
                        console.warn('Failed to parse watermark configuration:', e);
                    }
                }
            };
            xhr.send();
        }
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initWatermark);
    } else {
        initWatermark();
    }
    
    // Export to global scope for external access
    window.XWikiWatermark = WatermarkRenderer;
    
})();
