package com.microcredchina.xwikiwatermark;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for WatermarkConfiguration class.
 */
public class WatermarkConfigurationTest {
    
    private WatermarkConfiguration config;
    
    @BeforeEach
    void setUp() {
        config = new WatermarkConfiguration();
    }
    
    @Test
    void testDefaultValues() {
        assertFalse(config.isEnabled());
        assertEquals("${user} - ${timestamp}", config.getTextTemplate());
        assertEquals(200, config.getXSpacing());
        assertEquals(100, config.getYSpacing());
        assertEquals(-30, config.getAngle());
        assertEquals(0.1f, config.getOpacity(), 0.001f);
        assertEquals(14, config.getFontSize());
        assertFalse(config.isAntiCopy());
        assertTrue(config.isApplyToMobile());
    }
    
    @Test
    void testSettersWithValidValues() {
        config.setEnabled(true);
        config.setTextTemplate("Custom Template");
        config.setXSpacing(300);
        config.setYSpacing(150);
        config.setAngle(45);
        config.setOpacity(0.5f);
        config.setFontSize(18);
        config.setAntiCopy(true);
        config.setApplyToMobile(false);
        
        assertTrue(config.isEnabled());
        assertEquals("Custom Template", config.getTextTemplate());
        assertEquals(300, config.getXSpacing());
        assertEquals(150, config.getYSpacing());
        assertEquals(45, config.getAngle());
        assertEquals(0.5f, config.getOpacity(), 0.001f);
        assertEquals(18, config.getFontSize());
        assertTrue(config.isAntiCopy());
        assertFalse(config.isApplyToMobile());
    }
    
    @Test
    void testSettersWithInvalidValues() {
        // Test invalid spacing values (should use defaults)
        config.setXSpacing(-10);
        config.setYSpacing(0);
        assertEquals(WatermarkConfiguration.DEFAULT_X_SPACING, config.getXSpacing());
        assertEquals(WatermarkConfiguration.DEFAULT_Y_SPACING, config.getYSpacing());
        
        // Test invalid opacity values (should use default)
        config.setOpacity(-0.1f);
        assertEquals(WatermarkConfiguration.DEFAULT_OPACITY, config.getOpacity(), 0.001f);
        
        config.setOpacity(1.5f);
        assertEquals(WatermarkConfiguration.DEFAULT_OPACITY, config.getOpacity(), 0.001f);
        
        // Test invalid font size (should use default)
        config.setFontSize(-5);
        assertEquals(WatermarkConfiguration.DEFAULT_FONT_SIZE, config.getFontSize());
        
        // Test null text template (should use default)
        config.setTextTemplate(null);
        assertEquals(WatermarkConfiguration.DEFAULT_TEXT_TEMPLATE, config.getTextTemplate());
    }
    
    @Test
    void testOpacityBoundaries() {
        // Test valid boundary values
        config.setOpacity(0.0f);
        assertEquals(0.0f, config.getOpacity(), 0.001f);
        
        config.setOpacity(1.0f);
        assertEquals(1.0f, config.getOpacity(), 0.001f);
        
        // Test valid middle value
        config.setOpacity(0.25f);
        assertEquals(0.25f, config.getOpacity(), 0.001f);
    }
    
    @Test
    void testToString() {
        String result = config.toString();
        assertNotNull(result);
        assertTrue(result.contains("WatermarkConfiguration"));
        assertTrue(result.contains("enabled=false"));
        assertTrue(result.contains("textTemplate='${user} - ${timestamp}'"));
    }
}
