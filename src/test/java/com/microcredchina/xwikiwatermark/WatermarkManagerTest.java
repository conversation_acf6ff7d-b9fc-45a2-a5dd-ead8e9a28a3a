package com.microcredchina.xwikiwatermark;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for WatermarkManager class.
 * Simplified version focusing on core functionality without XWiki-specific dependencies.
 */
@ExtendWith(MockitoExtension.class)
public class WatermarkManagerTest {

    @Mock
    private WatermarkConfigurationService configurationService;

    private WatermarkManager watermarkManager;
    private WatermarkConfiguration testConfig;
    
    @BeforeEach
    void setUp() {
        watermarkManager = new WatermarkManager();

        // Use reflection to inject mocks (in real XWiki, this would be done by the container)
        try {
            java.lang.reflect.Field configField = WatermarkManager.class.getDeclaredField("configurationService");
            configField.setAccessible(true);
            configField.set(watermarkManager, configurationService);
        } catch (Exception e) {
            fail("Failed to inject mocks: " + e.getMessage());
        }

        // Set up test configuration
        testConfig = new WatermarkConfiguration();
        testConfig.setEnabled(true);
        testConfig.setTextTemplate("${user} - ${timestamp}");
    }
    
    @Test
    void testIsWatermarkEnabledWhenEnabled() {
        when(configurationService.getConfiguration()).thenReturn(testConfig);
        
        assertTrue(watermarkManager.isWatermarkEnabled());
        verify(configurationService).getConfiguration();
    }
    
    @Test
    void testIsWatermarkEnabledWhenDisabled() {
        testConfig.setEnabled(false);
        when(configurationService.getConfiguration()).thenReturn(testConfig);
        
        assertFalse(watermarkManager.isWatermarkEnabled());
        verify(configurationService).getConfiguration();
    }
    
    @Test
    void testIsWatermarkEnabledWithException() {
        when(configurationService.getConfiguration()).thenThrow(new RuntimeException("Test exception"));
        
        assertFalse(watermarkManager.isWatermarkEnabled());
        verify(configurationService).getConfiguration();
    }
    
    @Test
    void testGetConfiguration() {
        when(configurationService.getConfiguration()).thenReturn(testConfig);
        
        WatermarkConfiguration result = watermarkManager.getConfiguration();
        
        assertSame(testConfig, result);
        verify(configurationService).getConfiguration();
    }
    
    @Test
    void testResolvePlaceholdersWithUser() {
        // Test placeholder resolution - this will use Anonymous user since we don't have XWiki context
        String template = "User: ${user}";
        String result = watermarkManager.resolvePlaceholders(template);

        assertTrue(result.contains("Anonymous user"));
        assertTrue(result.startsWith("User: "));
    }
    
    @Test
    void testResolvePlaceholdersWithTimestamp() {
        String template = "Time: ${timestamp}";
        String result = watermarkManager.resolvePlaceholders(template);
        
        assertTrue(result.startsWith("Time: "));
        assertTrue(result.length() > "Time: ".length());
    }
    
    @Test
    void testResolvePlaceholdersWithMultiplePlaceholders() {
        String template = "${user} - ${timestamp}";
        String result = watermarkManager.resolvePlaceholders(template);

        assertTrue(result.contains("Anonymous user"));
        assertTrue(result.contains(" - "));
        assertTrue(result.length() > "Anonymous user - ".length());
    }
    
    @Test
    void testResolvePlaceholdersWithEmptyTemplate() {
        assertEquals("", watermarkManager.resolvePlaceholders(""));
        assertEquals("", watermarkManager.resolvePlaceholders(null));
        assertEquals("", watermarkManager.resolvePlaceholders("   "));
    }
    
    @Test
    void testResolvePlaceholdersWithUnknownPlaceholder() {
        String template = "Unknown: ${unknown}";
        String result = watermarkManager.resolvePlaceholders(template);
        
        assertEquals("Unknown: ${unknown}", result);
    }
    
    @Test
    void testGetConfigurationAsJson() {
        when(configurationService.getConfiguration()).thenReturn(testConfig);

        String json = watermarkManager.getConfigurationAsJson();

        assertNotNull(json);
        assertTrue(json.contains("\"enabled\":true"));
        assertTrue(json.contains("\"text\":\"Anonymous user"));
        assertTrue(json.contains("\"xSpacing\":200"));
        assertTrue(json.contains("\"ySpacing\":100"));
        assertTrue(json.contains("\"angle\":-30"));
        assertTrue(json.contains("\"opacity\":0.1"));
        assertTrue(json.contains("\"fontSize\":14"));
        assertTrue(json.contains("\"antiCopy\":false"));
        assertTrue(json.contains("\"applyToMobile\":true"));
    }
    
    @Test
    void testGetConfigurationAsJsonWithException() {
        when(configurationService.getConfiguration()).thenThrow(new RuntimeException("Test exception"));
        
        String json = watermarkManager.getConfigurationAsJson();
        
        assertEquals("{\"enabled\":false}", json);
    }
    
    @Test
    void testIsMobileDevice() {
        assertTrue(watermarkManager.isMobileDevice("Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)"));
        assertTrue(watermarkManager.isMobileDevice("Mozilla/5.0 (Android 10; Mobile; rv:81.0)"));
        assertTrue(watermarkManager.isMobileDevice("Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X)"));
        assertFalse(watermarkManager.isMobileDevice("Mozilla/5.0 (Windows NT 10.0; Win64; x64)"));
        assertFalse(watermarkManager.isMobileDevice(null));
    }
    
    @Test
    void testClearConfigurationCache() {
        watermarkManager.clearConfigurationCache();
        
        verify(configurationService).clearCache();
    }
}
